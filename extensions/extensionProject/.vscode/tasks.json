// See https://go.microsoft.com/fwlink/?LinkId=733558
// for the documentation about the tasks.json format
{
	"version": "2.0.0",
	"tasks": [
		{
			"type": "npm",
			"script": "compile-web",
			"group": {
				"kind": "build",
				"isDefault": true
			},
			"problemMatcher": [
				"$ts-webpack",
				"$tslint-webpack"
			]
		},
		{
			"type": "npm",
			"script": "watch-web",
			"group": "build",
			"isBackground": true,
			"problemMatcher": [
				"$ts-webpack-watch",
				"$tslint-webpack-watch"
			]
		}
	]
}
