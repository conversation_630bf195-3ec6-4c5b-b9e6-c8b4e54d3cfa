// A launch configuration that compiles the extension and then opens it inside a new window
// Use IntelliSense to learn about possible attributes.
// Hover to view descriptions of existing attributes.
// For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
{
	"version": "0.2.0",
	"configurations": [
		{
			"name": "Run Web Extension ",
			"type": "extensionHost",
			"debugWebWorkerHost": true,
			"request": "launch",
			"args": [
				"--extensionDevelopmentPath=${workspaceFolder}",
				"--extensionDevelopmentKind=web"
			],
			"outFiles": [
				"${workspaceFolder}/dist/web/**/*.js"
			],
			"preLaunchTask": "${defaultBuildTask}"
		},
		{
			"name": "Extension Tests",
			"type": "extensionHost",
			"debugWebWorkerHost": true,
			"request": "launch",
			"args": [
				"--extensionDevelopmentPath=${workspaceFolder}",
				"--extensionDevelopmentKind=web",
				"--extensionTestsPath=${workspaceFolder}/dist/web/test/suite/index"
			],
			"outFiles": [
				"${workspaceFolder}/dist/web/**/*.js"
			],
			"preLaunchTask": "${defaultBuildTask}"
		}
	]
}
