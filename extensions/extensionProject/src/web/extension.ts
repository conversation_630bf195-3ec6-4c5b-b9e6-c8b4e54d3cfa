// The module 'vscode' contains the VS Code extensibility API
// Import the module and reference it with the alias vscode in your code below
import * as vscode from 'vscode';

// This method is called when your extension is activated
// Your extension is activated the very first time the command is executed
export function activate(context: vscode.ExtensionContext) {

	// Use the console to output diagnostic information (console.log) and errors (console.error)
	// This line of code will only be executed once when your extension is activated
	console.log('Congratulations, your extension "extensionproject" is now active in the web extension host!');

	// 创建一个 TreeDataProvider 来提供视图内容
	const treeDataProvider = new MySearchTabProvider();

	// 注册树视图
	const treeView = vscode.window.createTreeView('mySearchTabView', {
		treeDataProvider: treeDataProvider,
		showCollapseAll: false
	});

	// The command has been defined in the package.json file
	// Now provide the implementation of the command with registerCommand
	// The commandId parameter must match the command field in package.json
	const disposable = vscode.commands.registerCommand('extensionproject.helloWorld', () => {
		// The code you place here will be executed every time your command is executed

		// Display a message box to the user
		vscode.window.showInformationMessage('Hello World from extensionProject in a web extension host!');
	});

	// 注册命令：打开新标签页
	const openNewTabCommand = vscode.commands.registerCommand('extensionproject.openNewTab', async () => {
		// 创建一个新的无标题文档
		const document = await vscode.workspace.openTextDocument({
			content: '# My Search Tab\n\n这是一个新的搜索标签页！\n\n你可以在这里进行搜索操作。',
			language: 'markdown'
		});

		// 在编辑器中显示文档
		await vscode.window.showTextDocument(document, {
			preview: false,
			viewColumn: vscode.ViewColumn.Active
		});
	});

	// 注册点击树项的命令
	const onDidSelectItem = vscode.commands.registerCommand('extensionproject.selectItem', (_item: MySearchTabItem) => {
		vscode.commands.executeCommand('extensionproject.openNewTab');
	});

	context.subscriptions.push(
		treeView,
		disposable,
		openNewTabCommand,
		onDidSelectItem
	);
}

class MySearchTabProvider implements vscode.TreeDataProvider<MySearchTabItem> {

	private _onDidChangeTreeData: vscode.EventEmitter<MySearchTabItem | undefined | null | void> = new vscode.EventEmitter<MySearchTabItem | undefined | null | void>();
	readonly onDidChangeTreeData: vscode.Event<MySearchTabItem | undefined | null | void> = this._onDidChangeTreeData.event;

	getTreeItem(element: MySearchTabItem): vscode.TreeItem {
		return element;
	}

	getChildren(element?: MySearchTabItem): Promise<MySearchTabItem[]> {
		if (!element) {
			// 根级别的项目
			return Promise.resolve([
				new MySearchTabItem('点击打开新标签页', vscode.TreeItemCollapsibleState.None, 'openTab')
			]);
		}
		return Promise.resolve([]);
	}
}

class MySearchTabItem extends vscode.TreeItem {

	constructor(
		label: string,
		collapsibleState: vscode.TreeItemCollapsibleState,
		public readonly itemType: string
	) {
		super(label, collapsibleState);

		this.tooltip = `${label}`;
		this.description = '';

		if (itemType === 'openTab') {
			this.command = {
				command: 'extensionproject.selectItem',
				title: 'Open New Tab',
				arguments: [this]
			};
			this.iconPath = new vscode.ThemeIcon('add');
		}
	}
}

// This method is called when your extension is deactivated
export function deactivate() { }
