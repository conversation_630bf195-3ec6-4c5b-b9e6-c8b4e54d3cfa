{"name": "new-tab-extension", "displayName": "New Tab Extension", "description": "A simple extension that adds a button to the Activity Bar to create new tabs", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "engines": {"vscode": "^1.5.0"}, "icon": "media/icon.png", "capabilities": {"virtualWorkspaces": true, "untrustedWorkspaces": {"supported": true}}, "activationEvents": ["onStartupFinished"], "main": "./out/extension", "contributes": {"viewsContainers": {"activitybar": [{"id": "new-tab-container", "icon": "$(search)", "title": "New Tab", "visibility": "visible"}]}, "views": {"new-tab-container": [{"id": "new-tab-view", "name": "New Tab"}]}, "commands": [{"command": "newTabExtension.createNewTab", "title": "Create New Tab", "icon": "$(add)"}], "menus": {"view/title": [{"command": "newTabExtension.createNewTab", "when": "view == new-tab-view", "group": "navigation"}]}}, "scripts": {"compile": "gulp compile-extension:new-tab-extension", "watch": "gulp watch-extension:new-tab-extension"}, "devDependencies": {"@types/node": "22.x", "@types/vscode": "^1.5.0"}}