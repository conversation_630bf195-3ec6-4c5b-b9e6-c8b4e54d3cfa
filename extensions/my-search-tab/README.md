# My Search Tab Extension

这是一个 VS Code 扩展，在 Activity Bar 中添加一个自定义按钮，点击后可以在编辑器中打开一个新的标签页。

## 功能

- 在 Activity Bar 中添加一个搜索图标按钮（使用与 search-result 扩展相同的图标）
- 点击按钮后在侧边栏显示一个自定义视图
- 视图中有一个选项，点击后在编辑器中打开新的标签页
- 新标签页包含 Markdown 格式的欢迎内容

## 文件结构

```
extensions/my-search-tab/
├── package.json                    # 扩展配置文件
├── tsconfig.json                   # TypeScript 配置
├── src/
│   └── extension.ts               # 主要扩展代码
├── out/
│   └── extension.js               # 编译后的 JavaScript 文件
├── images/
│   └── icon.png                   # 扩展图标（复制自 search-result）
├── extension.webpack.config.js     # Webpack 配置
├── extension-browser.webpack.config.js  # 浏览器版本 Webpack 配置
└── README.md                      # 说明文档
```

## 使用方法

1. 在 Activity Bar 中找到搜索图标（$(search)）
2. 点击图标打开侧边栏视图 "My Search Tab"
3. 在视图中点击"点击打开新标签页"选项
4. 新的标签页将在编辑器中打开，包含欢迎内容

## 技术实现

### 主要组件

1. **ViewContainer**: 在 Activity Bar 中注册一个新的容器
2. **TreeView**: 提供侧边栏中的树形视图
3. **TreeDataProvider**: 管理树形视图的数据
4. **Commands**: 处理用户交互和打开新标签页

### 关键配置

在 `package.json` 中的 `contributes` 部分：

- `viewsContainers.activitybar`: 定义 Activity Bar 中的按钮
- `views`: 定义侧边栏视图
- `commands`: 定义可执行的命令
- `menus`: 定义菜单项和按钮

### 编译

扩展已经成功编译，生成的 JavaScript 文件位于 `out/extension.js`。

## 开发说明

这个扩展基于 VS Code 内置的 search-result 扩展进行开发：

1. 使用相同的图标 (`images/icon.png`)
2. 参考了相似的项目结构
3. 使用标准的 VS Code 扩展 API
4. 支持 TypeScript 开发

## 下一步

要在 VS Code 中测试这个扩展，需要：

1. 确保 VS Code 开发环境正确设置
2. 将扩展添加到 VS Code 的扩展列表中
3. 启动 VS Code 开发版本进行测试

扩展代码已经编译成功，可以在 VS Code 扩展开发环境中进行测试和调试。
