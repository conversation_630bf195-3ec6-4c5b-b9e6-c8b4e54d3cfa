/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as vscode from 'vscode';

export function activate(context: vscode.ExtensionContext) {

    // 创建一个 TreeDataProvider 来提供视图内容
    const treeDataProvider = new MySearchTabProvider();

    // 注册树视图
    const treeView = vscode.window.createTreeView('mySearchTabView', {
        treeDataProvider: treeDataProvider,
        showCollapseAll: false
    });

    // 注册命令：打开新标签页
    const openNewTabCommand = vscode.commands.registerCommand('mySearchTab.openNewTab', async () => {
        // 创建一个新的无标题文档
        const document = await vscode.workspace.openTextDocument({
            content: '# My Search Tab\n\n这是一个新的搜索标签页！\n\n你可以在这里进行搜索操作。',
            language: 'markdown'
        });

        // 在编辑器中显示文档
        await vscode.window.showTextDocument(document, {
            preview: false,
            viewColumn: vscode.ViewColumn.Active
        });
    });

    // 注册点击树项的命令
    const onDidSelectItem = vscode.commands.registerCommand('mySearchTab.selectItem', (_item: MySearchTabItem) => {
        vscode.commands.executeCommand('mySearchTab.openNewTab');
    });

    context.subscriptions.push(
        treeView,
        openNewTabCommand,
        onDidSelectItem
    );
}

class MySearchTabProvider implements vscode.TreeDataProvider<MySearchTabItem> {

    private _onDidChangeTreeData: vscode.EventEmitter<MySearchTabItem | undefined | null | void> = new vscode.EventEmitter<MySearchTabItem | undefined | null | void>();
    readonly onDidChangeTreeData: vscode.Event<MySearchTabItem | undefined | null | void> = this._onDidChangeTreeData.event;

    getTreeItem(element: MySearchTabItem): vscode.TreeItem {
        return element;
    }

    getChildren(element?: MySearchTabItem): Promise<MySearchTabItem[]> {
        if (!element) {
            // 根级别的项目
            return Promise.resolve([
                new MySearchTabItem('点击打开新标签页', vscode.TreeItemCollapsibleState.None, 'openTab')
            ]);
        }
        return Promise.resolve([]);
    }
}

class MySearchTabItem extends vscode.TreeItem {

    constructor(
        label: string,
        collapsibleState: vscode.TreeItemCollapsibleState,
        public readonly itemType: string
    ) {
        super(label, collapsibleState);

        this.tooltip = `${label}`;
        this.description = '';

        if (itemType === 'openTab') {
            this.command = {
                command: 'mySearchTab.selectItem',
                title: 'Open New Tab',
                arguments: [this]
            };
            this.iconPath = new vscode.ThemeIcon('add');
        }
    }
}

export function deactivate() { }
