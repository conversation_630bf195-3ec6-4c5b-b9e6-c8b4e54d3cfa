#!/usr/bin/env node

/**
 * 简单的扩展测试脚本
 * 验证扩展的基本结构和配置
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 测试 My Search Tab 扩展...\n');

// 检查必要的文件
const requiredFiles = [
    'package.json',
    'src/extension.ts',
    'out/extension.js',
    'tsconfig.json',
    'images/icon.png'
];

let allFilesExist = true;

console.log('📁 检查文件结构:');
requiredFiles.forEach(file => {
    const filePath = path.join(__dirname, file);
    const exists = fs.existsSync(filePath);
    console.log(`  ${exists ? '✅' : '❌'} ${file}`);
    if (!exists) allFilesExist = false;
});

if (!allFilesExist) {
    console.log('\n❌ 某些必要文件缺失！');
    process.exit(1);
}

// 检查 package.json 配置
console.log('\n📋 检查 package.json 配置:');
const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, 'package.json'), 'utf8'));

const requiredFields = [
    'name',
    'displayName',
    'main',
    'activationEvents',
    'contributes'
];

requiredFields.forEach(field => {
    const exists = packageJson.hasOwnProperty(field);
    console.log(`  ${exists ? '✅' : '❌'} ${field}`);
});

// 检查 contributes 配置
if (packageJson.contributes) {
    console.log('\n🔧 检查 contributes 配置:');
    const contributes = packageJson.contributes;
    
    const hasViewsContainers = contributes.viewsContainers && contributes.viewsContainers.activitybar;
    const hasViews = contributes.views && contributes.views.mySearchTabContainer;
    const hasCommands = contributes.commands && Array.isArray(contributes.commands);
    
    console.log(`  ${hasViewsContainers ? '✅' : '❌'} viewsContainers.activitybar`);
    console.log(`  ${hasViews ? '✅' : '❌'} views.mySearchTabContainer`);
    console.log(`  ${hasCommands ? '✅' : '❌'} commands`);
}

// 检查编译后的文件
console.log('\n🔨 检查编译结果:');
const compiledFile = path.join(__dirname, 'out/extension.js');
if (fs.existsSync(compiledFile)) {
    const content = fs.readFileSync(compiledFile, 'utf8');
    const hasActivateFunction = content.includes('function activate');
    const hasDeactivateFunction = content.includes('function deactivate');
    
    console.log(`  ${hasActivateFunction ? '✅' : '❌'} activate 函数`);
    console.log(`  ${hasDeactivateFunction ? '✅' : '❌'} deactivate 函数`);
} else {
    console.log('  ❌ 编译后的文件不存在');
}

console.log('\n🎉 扩展基本结构检查完成！');
console.log('\n📝 下一步:');
console.log('  1. 在 VS Code 开发环境中加载此扩展');
console.log('  2. 按 F5 启动扩展开发主机');
console.log('  3. 在 Activity Bar 中查找搜索图标');
console.log('  4. 点击图标测试功能');
