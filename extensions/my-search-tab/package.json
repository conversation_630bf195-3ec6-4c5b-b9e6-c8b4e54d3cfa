{"name": "my-search-tab", "displayName": "My Search Tab", "description": "Add a custom tab in Activity Bar that opens a new editor tab", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "icon": "images/icon.png", "engines": {"vscode": "^1.39.0"}, "main": "./out/extension.js", "browser": "./dist/extension.js", "activationEvents": ["onView:mySearchTabView"], "scripts": {"vscode:prepublish": "node ../../node_modules/gulp/bin/gulp.js --gulpfile ../../build/gulpfile.extensions.js compile-extension:my-search-tab ./tsconfig.json"}, "capabilities": {"virtualWorkspaces": true, "untrustedWorkspaces": {"supported": true}}, "contributes": {"viewsContainers": {"activitybar": [{"id": "mySearchTabContainer", "title": "My Search Tab", "icon": "$(search)"}]}, "views": {"mySearchTabContainer": [{"id": "mySearchTabView", "name": "My Search Tab", "when": "true"}]}, "commands": [{"command": "mySearchTab.openNewTab", "title": "Open New Tab", "icon": "$(add)"}], "menus": {"view/title": [{"command": "mySearchTab.openNewTab", "when": "view == mySearchTabView", "group": "navigation"}]}}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}